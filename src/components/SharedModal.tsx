// SharedModal.tsx
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dalContent,
  Modal<PERSON>eader,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>oot<PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";

interface SharedModalProps {
  isOpen: boolean;
  onOpenChange: () => void;
  title?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  errorMessage?: string;
}

export function SharedModal({
  isOpen,
  onOpenChange,
  title,
  children,
  footer,
  errorMessage,
}: SharedModalProps) {
  return (
    <Modal
      size="5xl"
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      placement="top-center"
      scrollBehavior="inside"
    >
      <ModalContent>
        {(onClose) => (
          <>
            {title && <ModalHeader className="!p-8">{title}</ModalHeader>}
            <ModalBody>{children}</ModalBody>
            {footer && <ModalFooter>{footer}</ModalFooter>}
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
