import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  Logger,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { TodosService } from './todos.service';
import { CreateTodoDto } from './dto/create-todo.dto';
import { UpdateTodoDto } from './dto/update-todo.dto';
import { TodoResponseDto } from './dto/todo-response.dto';
import { TodoQueryDto } from './dto/todo-query.dto';

@ApiTags('Todos')
@Controller('todos')
@ApiBearerAuth()
export class TodosController {
  private readonly logger = new Logger(TodosController.name);

  constructor(private readonly todosService: TodosService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new todo' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Todo created successfully',
    type: TodoResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async create(
    @Body() createTodoDto: CreateTodoDto,
    @Request() req: any,
  ): Promise<TodoResponseDto> {
    const userId = req.user?.id || req.user?.sub || 'unknown';
    this.logger.log(`Creating todo for user: ${userId}`);
    return this.todosService.create(createTodoDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all todos with optional filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Todos retrieved successfully',
    type: [TodoResponseDto],
  })
  async findAll(@Query() query: TodoQueryDto): Promise<TodoResponseDto[]> {
    return this.todosService.findAll(query);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get todo statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Todo statistics retrieved successfully',
  })
  async getStats(@Request() req: any, @Query('user_id') userId?: string) {
    const requestUserId = userId || req.user?.id || req.user?.sub;
    return this.todosService.getStats(requestUserId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific todo by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Todo retrieved successfully',
    type: TodoResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Todo not found',
  })
  async findOne(@Param('id') id: string): Promise<TodoResponseDto> {
    return this.todosService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a todo' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Todo updated successfully',
    type: TodoResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Todo not found',
  })
  async update(
    @Param('id') id: string,
    @Body() updateTodoDto: UpdateTodoDto,
    @Request() req: any,
  ): Promise<TodoResponseDto> {
    const userId = req.user?.id || req.user?.sub || 'unknown';
    this.logger.log(`Updating todo ${id} by user: ${userId}`);
    return this.todosService.update(+id, updateTodoDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a todo' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Todo deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Todo not found',
  })
  async remove(@Param('id') id: string, @Request() req: any): Promise<void> {
    const userId = req.user?.id || req.user?.sub || 'unknown';
    this.logger.log(`Deleting todo ${id} by user: ${userId}`);
    return this.todosService.remove(+id, userId);
  }

  @Post('check-reminders')
  @ApiOperation({ summary: 'Manually trigger reminder check (for testing)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Reminder check completed',
  })
  async checkReminders(): Promise<{ message: string }> {
    await this.todosService.checkAndSendReminders();
    return { message: 'Reminder check completed' };
  }
}
