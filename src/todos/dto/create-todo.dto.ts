import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsDateString,
  IsArray,
  ValidateNested,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TodoStatus, TodoPriority } from '@prisma/client';

export class CreateTodoDto {
  @ApiProperty({ 
    description: 'Todo title',
    maxLength: 255,
    example: 'Review quarterly sales report'
  })
  @IsString()
  @MaxLength(255)
  title: string;

  @ApiPropertyOptional({ 
    description: 'Detailed description of the todo',
    example: 'Review and analyze Q4 sales performance, identify trends and prepare summary for board meeting'
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    enum: TodoPriority,
    description: 'Priority level of the todo',
    default: TodoPriority.MEDIUM,
    example: TodoPriority.HIGH
  })
  @IsEnum(TodoPriority)
  @IsOptional()
  priority?: TodoPriority = TodoPriority.MEDIUM;

  @ApiPropertyOptional({
    description: 'Due date for the todo (ISO string)',
    example: '2024-12-31T23:59:59.000Z'
  })
  @IsDateString()
  @IsOptional()
  due_date?: string;

  @ApiPropertyOptional({
    description: 'Reminder time for the todo (ISO string)',
    example: '2024-12-30T09:00:00.000Z'
  })
  @IsDateString()
  @IsOptional()
  reminder_time?: string;

  @ApiPropertyOptional({
    description: 'Admin user ID to assign this todo to',
    example: 'clm123abc456def789'
  })
  @IsString()
  @IsOptional()
  assigned_to?: string;

  @ApiPropertyOptional({
    description: 'Array of tags for categorization',
    example: ['sales', 'quarterly', 'urgent'],
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Additional metadata as JSON object',
    example: { category: 'business', department: 'sales' }
  })
  @IsOptional()
  metadata?: Record<string, any>;
}
