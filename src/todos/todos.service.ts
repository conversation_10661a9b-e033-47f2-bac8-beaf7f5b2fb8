import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { CreateTodoDto } from './dto/create-todo.dto';
import { UpdateTodoDto } from './dto/update-todo.dto';
import { TodoResponseDto } from './dto/todo-response.dto';
import { TodoQueryDto } from './dto/todo-query.dto';
import { TodoStatus, TodoPriority, Prisma } from '@prisma/client';
import { NotificationService } from '../purchases/notifications/notification.service';
import { NotificationType } from '@prisma/client';
import * as moment from 'moment';

@Injectable()
export class TodosService {
  private readonly logger = new Logger(TodosService.name);

  constructor(
    private database: DatabaseService,
    private notificationService: NotificationService,
  ) {}

  private convertToResponseDto(todo: any): TodoResponseDto {
    return {
      id: todo.id,
      title: todo.title,
      description: todo.description,
      status: todo.status,
      priority: todo.priority,
      due_date: todo.due_date,
      reminder_time: todo.reminder_time,
      completed_at: todo.completed_at,
      created_by: todo.created_by,
      assigned_to: todo.assigned_to,
      tags: todo.tags as string[],
      metadata: todo.metadata as Record<string, any>,
      createdAt: todo.createdAt,
      updatedAt: todo.updatedAt,
    };
  }

  async create(
    createTodoDto: CreateTodoDto,
    createdBy: string,
  ): Promise<TodoResponseDto> {
    try {
      const todoData: any = {
        title: createTodoDto.title,
        description: createTodoDto.description,
        priority: createTodoDto.priority || TodoPriority.MEDIUM,
        created_by: createdBy,
        assigned_to: createTodoDto.assigned_to,
        tags: createTodoDto.tags || [],
        metadata: createTodoDto.metadata || {},
      };

      if (createTodoDto.due_date) {
        todoData.due_date = new Date(createTodoDto.due_date);
      }

      if (createTodoDto.reminder_time) {
        todoData.reminder_time = new Date(createTodoDto.reminder_time);
      }

      const todo = await this.database.admin_todos.create({
        data: todoData,
      });

      this.logger.log(`Todo created with ID: ${todo.id} by user: ${createdBy}`);
      return this.convertToResponseDto(todo);
    } catch (error) {
      this.logger.error('Failed to create todo:', error);
      throw new HttpException(
        'Failed to create todo',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAll(query: TodoQueryDto): Promise<TodoResponseDto[]> {
    try {
      const limit = parseInt(query.limit || '50');
      const offset = parseInt(query.offset || '0');

      // Build where condition
      const where: Prisma.admin_todosWhereInput = {};

      if (query.status) {
        where.status = query.status;
      }

      if (query.priority) {
        where.priority = query.priority;
      }

      if (query.created_by) {
        where.created_by = query.created_by;
      }

      if (query.assigned_to) {
        where.assigned_to = query.assigned_to;
      }

      if (query.due_after || query.due_before) {
        where.due_date = {};
        if (query.due_after) {
          where.due_date.gte = new Date(query.due_after);
        }
        if (query.due_before) {
          where.due_date.lte = new Date(query.due_before);
        }
      }

      if (query.overdue_only === 'true') {
        where.due_date = {
          ...((where.due_date as Prisma.DateTimeFilter) || {}),
          lt: new Date(),
        };
        where.status = {
          not: TodoStatus.COMPLETED,
        };
      }

      if (query.tags) {
        const tagArray = query.tags.split(',').map((tag) => tag.trim());
        where.tags = {
          array_contains: tagArray,
        };
      }

      if (query.search) {
        where.OR = [
          { title: { contains: query.search } },
          { description: { contains: query.search } },
        ];
      }

      // Build orderBy
      const orderBy: Prisma.admin_todosOrderByWithRelationInput = {};
      const sortField = query.sort_by || 'createdAt';
      const sortOrder = query.sort_order === 'asc' ? 'asc' : 'desc';
      orderBy[sortField] = sortOrder;

      const todos = await this.database.admin_todos.findMany({
        where,
        take: limit,
        skip: offset,
        orderBy,
      });

      return todos.map((todo) => this.convertToResponseDto(todo));
    } catch (error) {
      this.logger.error('Failed to fetch todos:', error);
      throw new HttpException(
        'Failed to fetch todos',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: number): Promise<TodoResponseDto> {
    try {
      const todo = await this.database.admin_todos.findUnique({
        where: { id },
      });

      if (!todo) {
        throw new HttpException('Todo not found', HttpStatus.NOT_FOUND);
      }

      return this.convertToResponseDto(todo);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to fetch todo ${id}:`, error);
      throw new HttpException(
        'Failed to fetch todo',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async update(
    id: number,
    updateTodoDto: UpdateTodoDto,
    userId: string,
  ): Promise<TodoResponseDto> {
    try {
      // Check if todo exists
      const existingTodo = await this.database.admin_todos.findUnique({
        where: { id },
      });

      if (!existingTodo) {
        throw new HttpException('Todo not found', HttpStatus.NOT_FOUND);
      }

      const updateData: any = { ...updateTodoDto };

      // Handle date conversions
      if (updateTodoDto.due_date) {
        updateData.due_date = new Date(updateTodoDto.due_date);
      }

      if (updateTodoDto.reminder_time) {
        updateData.reminder_time = new Date(updateTodoDto.reminder_time);
      }

      // Set completed_at when status changes to COMPLETED
      if (
        updateTodoDto.status === TodoStatus.COMPLETED &&
        existingTodo.status !== TodoStatus.COMPLETED
      ) {
        updateData.completed_at = new Date();
      }

      // Clear completed_at when status changes from COMPLETED
      if (
        updateTodoDto.status &&
        updateTodoDto.status !== TodoStatus.COMPLETED &&
        existingTodo.status === TodoStatus.COMPLETED
      ) {
        updateData.completed_at = null;
      }

      const updatedTodo = await this.database.admin_todos.update({
        where: { id },
        data: updateData,
      });

      this.logger.log(`Todo ${id} updated by user: ${userId}`);
      return this.convertToResponseDto(updatedTodo);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to update todo ${id}:`, error);
      throw new HttpException(
        'Failed to update todo',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async remove(id: number, userId: string): Promise<void> {
    try {
      const todo = await this.database.admin_todos.findUnique({
        where: { id },
      });

      if (!todo) {
        throw new HttpException('Todo not found', HttpStatus.NOT_FOUND);
      }

      await this.database.admin_todos.delete({
        where: { id },
      });

      this.logger.log(`Todo ${id} deleted by user: ${userId}`);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to delete todo ${id}:`, error);
      throw new HttpException(
        'Failed to delete todo',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getStats(userId?: string): Promise<any> {
    try {
      const where: Prisma.admin_todosWhereInput = {};
      if (userId) {
        where.OR = [{ created_by: userId }, { assigned_to: userId }];
      }

      const [total, pending, inProgress, completed, overdue] =
        await Promise.all([
          this.database.admin_todos.count({ where }),
          this.database.admin_todos.count({
            where: { ...where, status: TodoStatus.PENDING },
          }),
          this.database.admin_todos.count({
            where: { ...where, status: TodoStatus.IN_PROGRESS },
          }),
          this.database.admin_todos.count({
            where: { ...where, status: TodoStatus.COMPLETED },
          }),
          this.database.admin_todos.count({
            where: {
              ...where,
              due_date: { lt: new Date() },
              status: { not: TodoStatus.COMPLETED },
            },
          }),
        ]);

      return {
        total,
        pending,
        inProgress,
        completed,
        overdue,
      };
    } catch (error) {
      this.logger.error('Failed to get todo stats:', error);
      throw new HttpException(
        'Failed to get todo statistics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Check for todos that need reminders and send notifications
   */
  async checkAndSendReminders(): Promise<void> {
    try {
      const now = new Date();
      const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

      console.log('Starting reminder check at:', now);

      const todosNeedingReminders = await this.database.admin_todos.findMany({
        where: {
          AND: [
            {
              reminder_time: {
                gte: fiveMinutesAgo,
                lte: fiveMinutesFromNow,
              },
            },
            {
              status: {
                notIn: [TodoStatus.COMPLETED, TodoStatus.CANCELLED],
              },
            },
          ],
        },
        select: {
          id: true,
          title: true,
          priority: true,
          assigned_to: true,
          created_by: true,
          due_date: true,
          reminder_time: true,
          description: true,
          metadata: true,
        },
      });

      console.log('Found todos:', todosNeedingReminders);

      const batchSize = 10;
      for (let i = 0; i < todosNeedingReminders.length; i += batchSize) {
        const batch = todosNeedingReminders.slice(i, i + batchSize);
        await Promise.all(
          batch.map(async (todo) => {
            try {
              console.log('Processing todo:', {
                id: todo.id,
                title: todo.title,
                reminder_time: todo.reminder_time,
              });

              const dueMessage = todo.due_date
                ? ` (Due: ${moment(todo.due_date).format(
                    'MMM DD, YYYY HH:mm',
                  )})`
                : '';

              const notificationMessage = `${todo.title}${dueMessage}${
                todo.description ? `\n${todo.description}` : ''
              }`;

              console.log(
                'Creating notification with message:',
                notificationMessage,
              );

              // Try to create notification
              try {
                console.log('Attempting to create notification with data:', {
                  title: `Todo Reminder: ${todo.title}`,
                  message: notificationMessage,
                  type: NotificationType.TODO_REMINDER,
                  metadata: {
                    todoId: todo.id,
                    todoTitle: todo.title,
                    priority: todo.priority,
                    assignedTo: todo.assigned_to,
                    createdBy: todo.created_by,
                    dueDate: todo.due_date,
                    reminderTime: todo.reminder_time,
                  },
                });

                await this.notificationService.createNotification({
                  title: `Todo Reminder: ${todo.title}`,
                  message: notificationMessage,
                  type: NotificationType.TODO_REMINDER,
                  metadata: {
                    todoId: todo.id,
                    todoTitle: todo.title,
                    priority: todo.priority,
                    assignedTo: todo.assigned_to,
                    createdBy: todo.created_by,
                    dueDate: todo.due_date,
                    reminderTime: todo.reminder_time,
                  },
                });
                console.log('Notification created successfully');
              } catch (notificationError) {
                console.error('Notification creation failed:', {
                  error: notificationError.message,
                  stack: notificationError.stack,
                  data: {
                    todoId: todo.id,
                    title: todo.title,
                    message: notificationMessage,
                    type: NotificationType.TODO_REMINDER,
                  },
                });
                throw notificationError;
              }

              // Try to update todo
              try {
                await this.database.admin_todos.update({
                  where: { id: todo.id },
                  data: {
                    reminder_time: null,
                    metadata: {
                      ...(typeof todo.metadata === 'object' &&
                      todo.metadata !== null
                        ? todo.metadata
                        : {}),
                      lastReminderSent: new Date().toISOString(),
                    },
                  },
                });
                console.log('Todo updated successfully');
              } catch (updateError) {
                console.error('Todo update failed:', {
                  error: updateError.message,
                  stack: updateError.stack,
                });
                throw updateError;
              }
            } catch (error) {
              // Detailed error logging
              console.error('Failed to process todo:', {
                todoId: todo.id,
                error: {
                  message: error.message,
                  stack: error.stack,
                  name: error.name,
                },
                todoData: {
                  id: todo.id,
                  title: todo.title,
                  reminder_time: todo.reminder_time,
                  due_date: todo.due_date,
                  metadata: todo.metadata,
                },
              });

              this.logger.error(
                `Failed to process reminder for todo ${todo.id}: ${error.message}\nStack: ${error.stack}`,
              );
            }
          }),
        );

        if (i + batchSize < todosNeedingReminders.length) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
    } catch (error) {
      console.error('Main reminder process failed:', {
        error: error.message,
        stack: error.stack,
      });

      this.logger.error(
        `Failed to check and send todo reminders: ${error.message}\nStack: ${error.stack}`,
      );
      throw new HttpException(
        'Failed to process todo reminders',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
