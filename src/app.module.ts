import {
  Middleware<PERSON>onsumer,
  <PERSON><PERSON><PERSON>,
  Lo<PERSON>,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { PurchasesModule } from './purchases/purchases.module';
import { MapcogModule } from './mapcog/mapcog.module';
import { RequestLoggerMiddleware } from './request-logger-middleware';
import { ScheduledTestsModule } from './scheduled_tests/scheduled_tests.module';
import { VergenceTestsModule } from './vergenceTest/vergence_tests.module';
import { AuthModule } from './auth/auth.module';
import { PasscodeModule } from './email_passcode/email_passcode.module';
import { UserModule } from './user/user.module';
import { StorageModule } from 'storage/storage.module';
import { SchoolModule } from './school/school.module';
import { NotificationModule } from './notifications/notification.module';
import { BinogiModule } from './binogi/binogi.module';
import { PDMeasurementModule } from './pd_measurement/pd_measurement.module';
import { TrainingDataModule } from './training-data/training-data.module';
import { RedisModule } from './redis/redis.module';

// Import FirebaseService and FirebaseAuthMiddleware
import { FirebaseService } from './firebase/firebase.service';
import { AuthMiddleware } from './firebase/firebaseAuthMiddleware';
import { FirebaseModule } from './firebase/firebase.module';
import { ScheduleModule } from '@nestjs/schedule';
import { TasksModule } from './purchases/tasks/tasks.module';
import { AdminAuthModule } from './auth_admin/adminAuth.module';
import { AdminActivitiesModule } from './admin_activities/adminActivities.module';
import { LoggerModule } from './opensearch/opensearch.module';
import { PurchaseNotificationModule } from './purchases/notifications/notification.module';
import { LocoModule } from './loco/loco.module';
import { TodosModule } from './todos/todos.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: process.env.NODE_ENV === 'local' ? 'local.env' : 'prod.env',
      isGlobal: true,
    }),
    DatabaseModule,
    PurchasesModule,
    MapcogModule,
    ScheduledTestsModule,
    VergenceTestsModule,
    AuthModule,
    PasscodeModule,
    UserModule,
    StorageModule,
    SchoolModule,
    NotificationModule,
    BinogiModule,
    FirebaseModule,
    ScheduleModule.forRoot(),
    TasksModule,
    AdminAuthModule,
    PDMeasurementModule,
    TrainingDataModule,
    AdminActivitiesModule,
    RedisModule,
    LoggerModule,
    PurchaseNotificationModule,
    LocoModule,
    TodosModule,
  ],
  controllers: [AppController],
  providers: [AppService, Logger],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(RequestLoggerMiddleware) // Apply logging globally
      .forRoutes('*');

    consumer
      .apply(AuthMiddleware) // Apply AuthMiddleware only to specific routes
      .exclude(
        { path: 'auth/signup', method: RequestMethod.POST },
        { path: 'passcode/email-passcode', method: RequestMethod.POST },
        { path: 'mapcog/fetchMapcog', method: RequestMethod.POST },
        { path: 'mapcog/login', method: RequestMethod.POST },
        { path: 'mapcog/web-login', method: RequestMethod.POST },
        { path: 'mapcog/register', method: RequestMethod.POST },
        { path: 'mapcog/getMapcogData', method: RequestMethod.POST },
        {
          path: 'purchases/linkPurchaseActivation',
          method: RequestMethod.POST,
        },
        { path: 'purchases/activate', method: RequestMethod.POST },
        { path: 'purchases/addPurchase', method: RequestMethod.POST },
        { path: 'purchases/purchaseInfo/:id', method: RequestMethod.GET },
        { path: 'user/submitSurvey', method: RequestMethod.POST },
        { path: 'user/link-guardian-account', method: RequestMethod.POST },
        {
          path: 'user/reset-child-account-password',
          method: RequestMethod.POST,
        },
      )
      .forRoutes('*'); // Apply AuthMiddleware to all other routes
  }

  constructor() {
    const envFile = process.env.NODE_ENV === 'local' ? 'local.env' : 'prod.env';
    console.log(`Running with environment file: ${envFile}`);
    console.log(`Database URL: ${process.env.DATABASE_URL}`);
  }
}
