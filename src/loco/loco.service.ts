import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class LocoService {

  constructor(private readonly httpService: HttpService) {}

  private getApiKey(type?: string): string {
    switch (type?.toLowerCase()) {
      case 'letters':
        return process.env.LETTERS_API_KEY;
      case 'words':
        return process.env.WORDS_API_KEY;
      case 'sentences':
        return process.env.SENTENCES_API_KEY;
    }
  }

  async getTranslations(lang: string, type?: string) {
    try {
      const apiKey = this.getApiKey(type);
      const { data } = await firstValueFrom(
        this.httpService.get(
          `https://localise.biz/api/export/locale/${lang}.json?key=${apiKey}`,
        ),
      );
      return data;
    } catch (error) {
      throw new Error(`Failed to fetch translations: ${error.message}`);
    }
  }
}
