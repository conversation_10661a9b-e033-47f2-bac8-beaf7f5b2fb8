import { Injectable, Logger, HttpStatus, HttpException } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import {
  CreateNotificationDto,
  NotificationResponseDto,
  NotificationQueryDto,
  MarkNotificationReadDto,
  NotificationCountDto,
  MetadataDto,
} from '../dto/notifications.dto';
import { NotificationType, Prisma } from '@prisma/client';
import { NotificationGateway } from './notification.gateway';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    private database: DatabaseService,
    private notificationGateway: NotificationGateway,
  ) {}

  private convertToResponseDto(notification: any): NotificationResponseDto {
    return {
      id: notification.id,
      title: notification.title,
      message: notification.message,
      type: notification.type,
      metadata: notification.metadata as MetadataDto,
      read: notification.read,
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt,
    };
  }

  async checkDuplicateNotification(
    notification: CreateNotificationDto,
  ): Promise<boolean> {
    try {
      const existingNotifications =
        await this.database.admin_notifications.findMany({
          where: {
            title: notification.title,
            message: notification.message,
            type: notification.type,
          },
        });

      // Compare metadata of each existing notification with the new one
      return existingNotifications.some((existing) => {
        // Convert both metadata objects to sorted JSON strings for comparison
        const existingMetadataStr = this.normalizeMetadata(existing.metadata);
        const newMetadataStr = this.normalizeMetadata(notification.metadata);

        return existingMetadataStr === newMetadataStr;
      });
    } catch (error) {
      this.logger.error('Error checking for duplicate notification:', error);
      return false;
    }
  }

  private normalizeMetadata(metadata: any): string {
    if (!metadata) return '';

    // Helper function to sort arrays within the metadata
    const sortArrays = (obj: any): any => {
      if (Array.isArray(obj)) {
        return [...obj].sort();
      }
      if (typeof obj === 'object' && obj !== null) {
        const sorted: any = {};
        Object.keys(obj)
          .sort()
          .forEach((key) => {
            sorted[key] = sortArrays(obj[key]);
          });
        return sorted;
      }
      return obj;
    };

    // Sort all arrays in the metadata and convert to string
    const normalizedMetadata = sortArrays(metadata);
    return JSON.stringify(normalizedMetadata);
  }

  async createNotification(
    notification: CreateNotificationDto,
  ): Promise<NotificationResponseDto[]> {
    try {
      // Check for duplicates
      const isDuplicate = await this.checkDuplicateNotification(notification);
      if (isDuplicate) {
        this.logger.log('Duplicate notification detected, skipping creation');
        return [];
      }
      const data: any = {
        title: notification.title,
        message: notification.message,
        type: notification.type,
        read: notification.read ?? false,
        metadata: notification.metadata as MetadataDto,
      };
      const newNotification = await this.database.admin_notifications.create({
        data,
      });

      // Convert to ResponseDto format
      const responseDto = this.convertToResponseDto(newNotification);

      // Broadcast the new notification to admin clients
      await this.notificationGateway.broadcastAdminNotification(responseDto);

      // Return as an array
      return [responseDto];
    } catch (error) {
      this.logger.error('Failed to create notification:', {
        error: error.message,
        stack: error.stack,
        notification: notification,
      });

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // Handle specific Prisma errors
        switch (error.code) {
          case 'P2002':
            throw new HttpException(
              'Duplicate notification',
              HttpStatus.CONFLICT,
            );
          case 'P2003':
            throw new HttpException(
              'Related record not found',
              HttpStatus.BAD_REQUEST,
            );
          default:
            throw new HttpException(
              `Database error: ${error.message}`,
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
      }
      throw new HttpException(
        'Failed to create notification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async processAndCreateNotifications(purchases: any[]) {
    try {
      const missingShippingPurchases = purchases.filter(
        (p) => !p.shipping_Info && p.orderStatus?.status === 'completed',
      );
      const data: CreateNotificationDto = {
        title: 'Missing Shipping Information',
        message: `${missingShippingPurchases.length} orders need shipping information`,
        type: NotificationType.SHIPPING_MISSING,
        metadata: {
          purchaseIds: missingShippingPurchases.map((p) => p.id),
        },
      };

      if (missingShippingPurchases.length > 0) {
        await this.createNotification(data);
      }
    } catch (error) {
      this.logger.error(
        'Failed to process and create notifications',
        error.stack,
      );
      throw new HttpException(
        'Failed to process and create notifications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllNotifications(
    query: NotificationQueryDto,
  ): Promise<NotificationResponseDto[]> {
    try {
      const { limit, offset = 0, unreadOnly, type, startDate, endDate } = query;
      // Build the where condition for filtering
      const where: Prisma.admin_notificationsWhereInput = {};

      // Add filters based on query parameters
      if (unreadOnly) {
        where.read = false;
      }

      if (type) {
        where.type = type;
      }

      // Date range filter
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }
        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      const notifications = await this.database.admin_notifications.findMany({
        where,
        take: Number(limit),
        skip: offset,
        orderBy: {
          createdAt: 'desc',
        },
      });

      return notifications.map((notification) =>
        this.convertToResponseDto(notification),
      );
    } catch (error) {
      this.logger.error('Error fetching notifications:', error);
      throw new HttpException(
        'Failed to fetch notifications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllUnreadNotifications(): Promise<NotificationResponseDto[]> {
    try {
      const unreadNotifications =
        await this.database.admin_notifications.findMany({
          where: {
            read: false,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

      return unreadNotifications.map((notification) =>
        this.convertToResponseDto(notification),
      );
    } catch (error) {
      this.logger.error('Error fetching unread notifications:', error);
      throw new HttpException(
        'Failed to fetch unread notifications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(id: number): Promise<NotificationResponseDto> {
    try {
      const notification = await this.database.admin_notifications.findUnique({
        where: { id },
      });

      if (!notification) {
        throw new HttpException('Notification not found', HttpStatus.NOT_FOUND);
      }

      return this.convertToResponseDto(notification);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error finding notification ${id}:`, error);
      throw new HttpException(
        'Failed to fetch notification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async markAsRead(
    id: number,
    markReadDto: MarkNotificationReadDto,
  ): Promise<NotificationResponseDto> {
    try {
      const updatedNotification =
        await this.database.admin_notifications.update({
          where: { id },
          data: { read: markReadDto.read },
        });

      const responseDto = this.convertToResponseDto(updatedNotification);

      // Broadcast the notification update to admin clients
      await this.notificationGateway.broadcastNotificationUpdate(responseDto);

      return responseDto;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // P2025 = Record not found
        if (error.code === 'P2025') {
          throw new HttpException(
            'Notification not found',
            HttpStatus.NOT_FOUND,
          );
        }
      }
      this.logger.error(`Error marking notification ${id} as read:`, error);
      throw new HttpException(
        'Failed to update notification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async markAllAsRead(): Promise<NotificationResponseDto[]> {
    try {
      // Mark all as read
      await this.database.admin_notifications.updateMany({
        where: { read: false },
        data: { read: true },
      });

      // Fetch all notifications that are now marked as read
      const notifications = await this.database.admin_notifications.findMany({
        orderBy: { createdAt: 'desc' },
      });

      const responseDtos = notifications.map((notification) =>
        this.convertToResponseDto(notification),
      );

      // Broadcast update to all connected admin clients
      for (const notification of responseDtos) {
        await this.notificationGateway.broadcastNotificationUpdate(
          notification,
        );
      }

      return responseDtos;
    } catch (error) {
      this.logger.error('Error marking all notifications as read:', error);
      throw new HttpException(
        'Failed to update notifications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getCount(): Promise<NotificationCountDto> {
    try {
      // Use a transaction to ensure consistent counts
      const [total, unread] = await this.database.$transaction([
        this.database.admin_notifications.count(),
        this.database.admin_notifications.count({
          where: { read: false },
        }),
      ]);

      return { total, unread };
    } catch (error) {
      this.logger.error('Error counting notifications:', error);
      throw new HttpException(
        'Failed to count notifications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async delete(id: number): Promise<void> {
    try {
      // Get the notification before deleting it for broadcasting the update
      const notification = await this.database.admin_notifications.findUnique({
        where: { id },
      });

      if (!notification) {
        throw new HttpException('Notification not found', HttpStatus.NOT_FOUND);
      }

      // Delete the notification
      await this.database.admin_notifications.delete({
        where: { id },
      });

      // Broadcast the deletion (can send a special event or use the same update event)
      const responseDto = this.convertToResponseDto({
        ...notification,
        deleted: true,
      });

      await this.notificationGateway.broadcastNotificationUpdate(responseDto);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // P2025 = Record not found
        if (error.code === 'P2025') {
          throw new HttpException(
            'Notification not found',
            HttpStatus.NOT_FOUND,
          );
        }
      }
      this.logger.error(`Error deleting notification ${id}:`, error);
      throw new HttpException(
        'Failed to delete notification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
