import { Modu<PERSON> } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { DatabaseModule } from 'src/database/database.module';
import { PurchasesService } from '../purchases.service';
import { RedisModule } from '../../redis/redis.module';
import { TasksController } from './tasks.controller';
import { PurchaseNotificationModule } from '../notifications/notification.module';
import { TodosModule } from '../../todos/todos.module';
import { TodosService } from '../../todos/todos.service';

@Module({
  controllers: [TasksController],
  providers: [TasksService, PurchasesService, TodosService],
  imports: [
    DatabaseModule,
    RedisModule,
    PurchaseNotificationModule,
    TodosModule,
  ],
})
export class TasksModule {}
