import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DatabaseService } from 'src/database/database.service';
import { PurchasesService } from '../purchases.service';
import axios from 'axios';
import { Decimal } from '@prisma/client/runtime/library';
import { NotificationService } from '../notifications/notification.service';
import { NotificationType } from '@prisma/client';

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name);
  constructor(
    private database: DatabaseService,
    private readonly purchasesService: PurchasesService,
    private readonly notificationService: NotificationService,
  ) {}

  async convertTxtToJSON(text) {
    const lines = text.trim().replace(/;/g, ',').replace(/<br>/g, ',');
    const resultArr = lines.split(',');
    const removeLast = resultArr.slice(0, resultArr.length - 1);
    const result = [];
    for (let i = 0; i < removeLast.length; i += 3) {
      try {
        const purchase = await this.purchasesService.findPurchaseByOrderNumber(
          removeLast[i],
        );
        if (purchase) {
          const toISODate = new Date(removeLast[i + 2]);
          const shippingDate = toISODate.toISOString();
          result.push({
            purchase_id: purchase.id,
            tracking_number: removeLast[i + 1],
            shipping_date: shippingDate,
          });
        } else {
          this.logger.warn(
            `Purchase not found for order number: ${removeLast[i]}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error processing order number ${removeLast[i]}: ${error.message}`,
        );
      }
    }
    return result;
  }

  private async fetchTrackingDataFromE3PL(
    formattedDate: string,
  ): Promise<string> {
    const url = `https://www.e3pl.se/system/api.asp?s=443ce94115bf88d519dc6ce2c7489d59&typ=skickat&order=alla&datum=${formattedDate}&data=txt`;
    try {
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to fetch data from E3PL: ${error.message}`);
      throw new Error('Failed to fetch tracking data from E3PL');
    }
  }

  private async saveShippingInfo(shippingData: any[]): Promise<void> {
    for (const data of shippingData) {
      try {
        await this.database.shipping_info.upsert({
          where: {
            purchase_id: data.purchase_id,
          },
          update: {
            tracking_number: data.tracking_number,
            shipping_date: data.shipping_date,
          },
          create: {
            purchase_id: data.purchase_id,
            tracking_number: data.tracking_number,
            shipping_date: data.shipping_date,
          },
        });
      } catch (error) {
        this.logger.error(
          `Failed to save shipping info for purchase ${data.purchase_id}: ${error.message}`,
        );
        // Continue with the next record instead of throwing the error
        continue;
      }
    }
  }

  async getTrackingInfo(): Promise<void> {
    try {
      // Get formatted date
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];

      // Fetch data from E3PL
      const rawData = await this.fetchTrackingDataFromE3PL(formattedDate);

      // Convert raw data to JSON format
      const shippingData = await this.convertTxtToJSON(rawData);

      // Save to database
      await this.saveShippingInfo(shippingData);

      this.logger.log(
        `Successfully processed tracking info for date: ${formattedDate}`,
      );
    } catch (error) {
      this.logger.error(`Failed to process tracking info: ${error.message}`);
      throw error;
    }
  }

  async fetchWooCommerceOrders() {
    try {
      const response = await axios.get(process.env.WOOCOMMERCE_ORDER_URL, {
        headers: {
          'Content-Type': 'application/json',
        },
        auth: {
          username: process.env.WOO_API_KEY,
          password: process.env.WOO_API_SECERT,
        },
      });
      return response.data;
    } catch (error) {
      this.logger.error(`Error fetching WooCommerce orders: ${error.message}`);
      throw error;
    }
  }

  async updateOrderStatus() {
    const wooCommerceOrders = await this.fetchWooCommerceOrders();
    for (const order of wooCommerceOrders) {
      const orderId = order.id.toString();
      try {
        const purchase =
          await this.purchasesService.findPurchaseByOrderNumber(orderId);
        await this.database.orderStatus.upsert({
          where: { order_id: orderId },
          update: { status: order.status },
          create: {
            order_id: orderId,
            purchase_id: purchase ? purchase.id : null,
            status: order.status,
          },
        });
      } catch (error) {
        this.logger.error(
          `Error updating order status for order ${orderId}: ${error.message}`,
        );
      }
    }
    console.log("I'm executed on a schedule! Order status updated");
  }

  @Cron(CronExpression.EVERY_3_HOURS)
  async handleCron() {
    this.logger.debug('Fetching shipping info every 3 hours and push it to db');
    this.getTrackingInfo();
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async handleHalfHourCron() {
    this.logger.debug(
      'Fetching and updating WooCommerce order statuses - every half hour',
    );
    await this.updateOrderStatus();
  }

  // woocomerce orders addresses migraition

  async migrateWooCommerceAddresses() {
    const methodName = 'migrateWooCommerceAddresses';
    this.logger.log(`Starting WooCommerce address migration`);

    try {
      const orders = await this.fetchWooCommerceOrders();
      let migratedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;

      for (const order of orders) {
        try {
          // Find matching purchase by order number
          const purchase =
            await this.purchasesService.findPurchaseByOrderNumber(
              order.id.toString(),
            );

          if (!purchase) {
            this.logger.warn(
              `No matching purchase found for order ${order.id}`,
            );
            skippedCount++;
            continue;
          }

          // Check if additional info already exists for this purchase
          const existingAdditionalInfo =
            await this.database.purchase_additional_info.findFirst({
              where: {
                purchase_id: purchase.id,
              },
            });

          // Prepare address data
          const addressData = {
            address_line: this.combineAddressLines(
              order.billing.address_1,
              order.billing.address_2,
            ),
            city: order.billing.city,
            state: order.billing.state,
            postal_code: order.billing.postcode,
            country: order.billing.country,
            phone: order.billing.phone,
            order_amount: new Decimal(order.total),
          };

          if (existingAdditionalInfo) {
            // Update existing record
            await this.database.purchase_additional_info.update({
              where: {
                id: existingAdditionalInfo.id, // Use the primary key 'id'
              },
              data: addressData,
            });
          } else {
            // Create new record
            await this.database.purchase_additional_info.create({
              data: {
                ...addressData,
                purchase_id: purchase.id,
              },
            });
          }

          migratedCount++;
          this.logger.log(
            `Successfully migrated address for order ${order.id} to purchase ${purchase.id}`,
          );
        } catch (error) {
          errorCount++;
          this.logger.error(
            `Error migrating address for order ${order.id}: ${error.message}`,
            error.stack,
          );
        }
      }

      return {
        total: orders.length,
        migrated: migratedCount,
        skipped: skippedCount,
        errors: errorCount,
      };
    } catch (error) {
      this.logger.error(
        `${methodName}: Failed to migrate WooCommerce addresses: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Failed to migrate WooCommerce addresses: ${error.message}`,
      );
    }
  }

  // Helper method to combine address lines
  private combineAddressLines(address1?: string, address2?: string): string {
    const lines = [address1, address2].filter((line) => line && line.trim());
    return lines.join(' ');
  }

  @Cron(CronExpression.EVERY_12_HOURS)
  async handlePurchaseUpdates() {
    try {
      await this.checkMissingShippingInfo();
      await this.checkCompletedOrders();
      await this.checkExpiringSoon();
    } catch (error) {
      this.logger.error('Error processing purchase notifications:', error);
      throw error;
    }
  }

  /**
   * Checks for purchases that need shipping information and creates notifications
   */
  private async checkMissingShippingInfo() {
    try {
      // Calculate date range: 1 week ago to 2 months ago
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      const twoMonthsAgo = new Date();
      twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);

      const purchases = await this.database.purchase.findMany({
        where: {
          shipping_Info: null, // No shipping info
          created_at: {
            gte: twoMonthsAgo, // Greater than or equal to 2 months ago
            lte: oneWeekAgo, // Less than or equal to 1 week ago
          },
          additional_info: {
            some: {
              purchase_type: 'START_PACKAGE',
            },
          },
          orderStatus: {
            AND: [
              {
                status: 'completed',
              },
              {
                order_id: {
                  lt: '999999999', // Less than 9 digits
                },
              },
            ],
          },
        },
        include: {
          shipping_Info: true,
          orderStatus: true,
          additional_info: true,
        },
      });

      await this.notificationService.processAndCreateNotifications(purchases);

      this.logger.debug(
        `Checked ${
          purchases.length
        } purchases for missing shipping information (created between ${twoMonthsAgo.toISOString()} and ${oneWeekAgo.toISOString()})`,
      );
    } catch (error) {
      this.logger.error('Error checking for missing shipping info:', error);
      throw error;
    }
  }

  /**
   * Checks for newly completed orders and creates notifications
   */
  private async checkCompletedOrders() {
    try {
      // Get today's date as a full ISO string
      const today = new Date().toISOString().split('T')[0] + 'T00:00:00.000Z';

      // Find order statuses that are 'completed' and associated with purchases created today
      const recentlyCompletedOrders = await this.database.orderStatus.findMany({
        where: {
          status: 'completed',
          purchase: {
            created_at: {
              gte: today,
            },
          },
        },
        include: {
          purchase: true,
        },
      });

      if (recentlyCompletedOrders.length > 0) {
        const notification = {
          title: 'New Completed Orders Today',
          message: `${recentlyCompletedOrders.length} new orders have been completed today`,
          type: NotificationType.PURCHASE_STATUS,
          metadata: {
            orderIds: recentlyCompletedOrders.map((order) => order.order_id),
            purchaseIds: recentlyCompletedOrders
              .filter((order) => order.purchase_id !== null)
              .map((order) => order.purchase_id),
          },
        };

        await this.notificationService.createNotification(notification);
        this.logger.debug(
          `Created notification for ${recentlyCompletedOrders.length} orders completed today`,
        );
      }
    } catch (error) {
      this.logger.error('Error checking for completed orders:', error);
      throw error;
    }
  }

  /**
   * Checks for subscriptions expiring soon and creates notifications
   */
  private async checkExpiringSoon() {
    try {
      // Look for users with subscriptions expiring in the next 7 days
      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

      const expiringUsers = await this.database.user.findMany({
        where: {
          valid_until: {
            gte: new Date(), // Not expired yet
            lte: sevenDaysFromNow, // But expiring soon
          },
          deleted: false,
        },
        include: {
          additional_info: true,
        },
      });

      if (expiringUsers.length > 0) {
        // Create a notification for expiring subscriptions
        const notification = {
          title: 'Subscriptions Expiring Soon',
          message: `${expiringUsers.length} user subscriptions are expiring within 7 days`,
          type: NotificationType.TRAINING_REMINDER,
          metadata: {
            userIds: expiringUsers.map((user) => user.id),
            expiryDates: expiringUsers.map((user) => ({
              userId: user.id,
              expiryDate: user.valid_until,
            })),
          },
        };

        await this.notificationService.createNotification(notification);
        this.logger.debug(
          `Created notification for ${expiringUsers.length} expiring subscriptions`,
        );
      }
    } catch (error) {
      this.logger.error('Error checking for expiring subscriptions:', error);
    }
  }
}
