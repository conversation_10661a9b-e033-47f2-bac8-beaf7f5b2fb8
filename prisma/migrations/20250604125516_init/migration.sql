-- AlterTable
ALTER TABLE `admin_accounts` MODIFY `role` ENUM('<PERSON><PERSON><PERSON>ADMIN', 'ADMI<PERSON>', 'SUBADMIN', 'EDITOR', 'TEACHER', 'USER', 'MARKETING') NOT NULL DEFAULT 'USER';

-- AlterTable
ALTER TABLE `admin_notifications` MODIFY `title` VARCHAR(255) NOT NULL,
    MODIFY `type` ENUM('SHIPPING_MISSING', 'PURCHASE_STATUS', 'TRAINING_REMINDER', 'TODO_REMINDER') NOT NULL;

-- AlterTable
ALTER TABLE `admin_user` MODIFY `role` ENUM('SUPERADMIN', 'ADMIN', 'SUBADMIN', 'EDITOR', 'TEACHER', 'USER', 'MARKETING') NOT NULL DEFAULT 'USER';

-- CreateTable
CREATE TABLE `admin_todos` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `status` ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'PENDING',
    `priority` ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') NOT NULL DEFAULT 'MEDIUM',
    `due_date` DATETIME(3) NULL,
    `reminder_time` DATETIME(3) NULL,
    `completed_at` DATETIME(3) NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `assigned_to` VARCHAR(191) NULL,
    `tags` JSON NULL,
    `metadata` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `admin_todos_created_by_idx`(`created_by`),
    INDEX `admin_todos_assigned_to_idx`(`assigned_to`),
    INDEX `admin_todos_status_idx`(`status`),
    INDEX `admin_todos_due_date_idx`(`due_date`),
    INDEX `admin_todos_reminder_time_idx`(`reminder_time`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
