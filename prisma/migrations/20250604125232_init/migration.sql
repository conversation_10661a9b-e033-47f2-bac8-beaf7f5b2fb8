-- AlterTable
ALTER TABLE `admin_accounts` M<PERSON><PERSON>Y `role` ENUM('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'EDITO<PERSON>', 'TEACHER', 'USER', 'MARKETING') NOT NULL DEFAULT 'USER';

-- AlterTable
ALTER TABLE `admin_user` <PERSON><PERSON><PERSON>Y `role` ENUM('ADMI<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'EDITO<PERSON>', 'TEACHER', 'USER', 'MARKETING') NOT NULL DEFAULT 'USER';

-- CreateTable
CREATE TABLE `admin_notifications` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(191) NOT NULL,
    `message` VARCHAR(191) NOT NULL,
    `type` ENUM('SHIPPING_MISSING', 'PURCHASE_STATUS', 'TRAINING_REMINDER') NOT NULL,
    `read` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `metadata` <PERSON><PERSON><PERSON> NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
