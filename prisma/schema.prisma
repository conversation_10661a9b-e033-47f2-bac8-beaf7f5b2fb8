generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "linux-arm64-openssl-1.1.x"]
}

datasource db {
  provider          = "mysql"
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("DATABASE_SHADOW_URL")
}

model calibration {
  id               Int                @id @default(autoincrement())
  user_id          Int?
  calibration_date DateTime?          @db.DateTime(0)
  deviceId         Int?               @map("device_id")
  user             user?              @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "calibration_ibfk_1")
  calibration_data calibration_data[]

  @@index([user_id], map: "user_id")
  @@index([deviceId], map: "fk_calibration_device")
}

model calibration_data {
  id             Int          @id @default(autoincrement())
  value          Decimal?     @db.Decimal(20, 15)
  calibration_id Int?
  calibration    calibration? @relation(fields: [calibration_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "calibration_data_ibfk_1")

  @@index([calibration_id], map: "calibration_id")
}

model comments {
  id         Int      @id @default(autoincrement())
  user_id    Int
  text       String   @db.Text
  created_on DateTime @db.DateTime(0)
  user       user     @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "comments_ibfk_1")

  @@index([user_id], map: "user_id")
}

model device_data {
  id             Int      @id @default(autoincrement())
  deviceId       String   @db.VarChar(255)
  deviceOffset   Int?
  height         Int?
  pixelDensity   Int?
  width          Int?
  mmfor1Pixel    Decimal? @db.Decimal(5, 2)
  model          String?  @db.VarChar(255)
  os             String?  @db.VarChar(255)
  pixelfor1mm    Decimal? @db.Decimal(5, 2)
  userId         Int?     @map("user_id")
  diagonalLength Decimal? @db.Decimal(8, 2)
  deviceChecksum String?  @map("device_checksum") @db.VarChar(64)

  @@index([userId], map: "fk_user_device")
}

model mapcog_legPers {
  dateAdded   String?       @db.VarChar(30)
  username    String        @id @db.VarChar(50)
  password    String?       @db.VarChar(50)
  logindef    String?       @default("U") @db.VarChar(5)
  mapcog_test mapcog_test[]
}

model mapcog_patient {
  legPersId String? @db.VarChar(50)
  sent      String? @db.VarChar(1)
  patientId String? @db.VarChar(50)
  firstName String? @db.Text
  lastName  String? @db.Text
  age       Int?
  gender    String? @db.VarChar(10)
  dateAdded String? @db.VarChar(30)
  origin    String? @db.VarChar(10)
  id        Int     @id

  @@index([legPersId], map: "mapcog_patient_mapcog_legPers_username_fk")
}

model mapcog_pause {
  id          Int  @id @default(autoincrement())
  resultId    Int?
  pauseTime   Int?
  pauseLength Int?

  @@index([resultId], map: "resultId")
}

model mapcog_result {
  orderIndex Int?
  duration   Int?
  id         Int  @id @default(autoincrement())
  testId     Int?

  @@index([testId], map: "testId")
}

model mapcog_test {
  sent           String?         @db.VarChar(1)
  id             Int             @id @default(autoincrement())
  dateAdded      String?         @db.VarChar(30)
  legPersId      String?         @db.VarChar(50)
  patientId      String?         @db.VarChar(50)
  medicine       String?         @db.Text
  dose           String?         @db.Text
  diagnosis      String?         @db.Text
  comments       String?         @db.Text
  mapcog_legPers mapcog_legPers? @relation(fields: [legPersId], references: [username], onDelete: Restrict, onUpdate: Restrict, map: "mapcog_test_ibfk_1")

  @@index([legPersId], map: "legPersId")
  @@index([patientId], map: "fk_patientId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model reading_chain {
  ID Int

  @@index([ID], map: "ID")
  @@ignore
}

model scheduled_notifications {
  id                 Int     @id @default(autoincrement())
  user_id            Int?
  scheduled_datetime String? @db.VarChar(255)
  status             String? @db.VarChar(255)
  title              String? @db.VarChar(255)
  body               String? @db.VarChar(255)
}

model scheduled_test {
  id                  Int       @id @default(autoincrement())
  user_id             Int?
  test_id             Int?
  scheduled_datetime  DateTime? @db.Timestamp(0)
  completion_datetime DateTime? @db.Timestamp(0)
  created_at          DateTime? @db.Timestamp(0)
  updated_at          DateTime? @db.Timestamp(0)
  is_mandatory        Boolean?
  notice_period       Int?      @default(3)
  user                user?     @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "scheduled_test_ibfk_1")
  test                test?     @relation(fields: [test_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "scheduled_test_ibfk_2")

  @@index([user_id], map: "scheduled_test_ibfk_1")
  @@index([test_id], map: "scheduled_test_ibfk_2")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model sub_admin_assigned_users {
  subAdminId     Int?
  userId         Int?
  sub_admin_data sub_admin_data? @relation(fields: [subAdminId], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "sub_admin_assigned_users_ibfk_1")
  user           user?           @relation(fields: [userId], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "sub_admin_assigned_users_ibfk_2")

  @@index([subAdminId], map: "subAdminId")
  @@index([userId], map: "userId")
  @@ignore
}

model sub_admin_data {
  id                       Int                        @id @default(autoincrement())
  Email                    String                     @db.VarChar(255)
  FirstName                String?                    @db.VarChar(255)
  LastName                 String?                    @db.VarChar(255)
  MobileNumber             String?                    @db.VarChar(255)
  sub_admin_assigned_users sub_admin_assigned_users[]
}

model test {
  id             Int              @id @default(autoincrement())
  name           String?          @db.VarChar(255)
  name_sv        String?          @db.VarChar(255)
  created_at     DateTime         @db.Timestamp(0)
  updated_at     DateTime         @db.Timestamp(0)
  is_mandatory   Boolean          @default(false)
  notice_period  Int?             @default(3)
  color          String           @db.VarChar(255)
  active         Boolean?         @default(true)
  scheduled_test scheduled_test[]
}

model training_session {
  id               Int       @id @default(autoincrement())
  user_id          Int?
  start_datetime   DateTime? @db.DateTime(0)
  end_datetime     DateTime? @db.DateTime(0)
  type             Int?
  source           Int?
  offset           Float?
  oscillation_time Int?
  pendulum_length  Float?
  speed            Float?
  duration         Float?
  created_at       DateTime? @default(now()) @db.Timestamp(0)
  updated_at       DateTime? @default(now()) @db.Timestamp(0)
  user             user?     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "training_session_ibfk_1")

  @@index([user_id], map: "user_id")
}

model user {
  id                       Int                        @id @default(autoincrement())
  uuid                     String?                    @db.VarChar(255)
  valid_until              DateTime?                  @db.DateTime(0)
  registered_on            DateTime?                  @db.Timestamp(0)
  starred                  Boolean?                   @default(false)
  type                     String?                    @db.VarChar(100)
  deleted                  Boolean?                   @default(false)
  Product                  String?                    @db.Char(50)
  parent_info_id           Int?
  calibration              calibration[]
  comments                 comments[]
  scheduled_test           scheduled_test[]
  sub_admin_assigned_users sub_admin_assigned_users[]
  training_session         training_session[]
  vergence_user_session    vergence_user_session[]
}

model user_additional_info {
  id                            Int       @id @default(autoincrement())
  name                          String?   @db.VarChar(200)
  email                         String?   @db.VarChar(200)
  age                           Int?
  vision_problem                String?   @db.VarChar(200)
  optional_text                 String?   @db.Text
  accept_newsletter             Boolean?  @default(false)
  user_id                       Int
  first_name                    String?   @db.VarChar(200)
  last_name                     String?   @db.VarChar(200)
  birthdate                     DateTime? @db.Date
  notification_hour             Int?
  training_question_id          Int       @default(1)
  change_flag                   Boolean?  @default(false)
  read_calibration_from_backend Boolean?  @default(false)
  performed_test_today          Boolean?  @default(false)
  has_double_vision             Boolean?  @default(false)
  starred                       Boolean   @default(false)

  @@index([user_id], map: "user_additional_info_user_fk")
}

model parent_info {
  id         Int     @id @default(autoincrement())
  first_name String? @db.VarChar(255)
  last_name  String? @db.VarChar(255)
  email      String? @db.VarChar(255)
}

model user_guardian {
  user_id     Int
  guardian_id Int
  id          Int @id @default(autoincrement())
}

model vergence_answer {
  id                   Int                    @id @default(autoincrement())
  vergence_question_id Int
  deleted              Boolean?               @default(false)
  answer               String?                @db.VarChar(100)
  value                Int?
  vergence_question    vergence_question      @relation(fields: [vergence_question_id], references: [id], onUpdate: Restrict, map: "vergence_answer_question_fk")
  vergence_user_answer vergence_user_answer[]

  @@index([vergence_question_id], map: "vergence_answer_question_fk")
}

model vergence_question {
  id       Int               @id @default(autoincrement())
  title    String?           @db.VarChar(500)
  language String?           @db.VarChar(20)
  deleted  Boolean?          @default(false)
  sequence Int?              @db.SmallInt
  answers  vergence_answer[]
}

model vergence_user_answer {
  id                    Int                    @id @default(autoincrement())
  session_id            Int?
  answer_id             Int?
  deleted               Boolean                @default(false)
  answer                vergence_answer?       @relation(fields: [answer_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "vergence_user_answer_vergence_answer_fk")
  vergence_user_session vergence_user_session? @relation(fields: [session_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "vergence_user_answer_vergence_user_session_fk")

  @@index([answer_id], map: "vergence_user_answer_vergence_answer_fk")
  @@index([session_id], map: "vergence_user_answer_vergence_user_session_fk")
}

model vergence_user_session {
  id                      Int                    @id @default(autoincrement())
  user_id                 Int?
  implemented_by_guardian Boolean?               @default(false)
  created_at              DateTime?              @db.DateTime(0)
  deleted                 Boolean                @default(false)
  vergence_user_answer    vergence_user_answer[]
  user                    user?                  @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "vergence_user_session_user__fk")

  @@index([user_id], map: "vergence_user_session_user__fk")
}

model pdMeasurement {
  id              Int      @id @default(autoincrement())
  optician_number Float    @db.Float
  measured_number Float    @db.Float
  created_at      DateTime @default(now()) @db.DateTime(0)

  @@map("pd_measurement")
}

model postTrainingData {
  id          Int      @id @default(autoincrement())
  user_id     Int
  question_id Int
  question    String   @db.VarChar(100)
  answer_id   Int
  answer      String   @db.VarChar(100)
  created_at  DateTime @default(now()) @db.DateTime(0)

  @@map("training_questions_data")
}

model Purchase {
  id                   Int                        @id @default(autoincrement())
  email                String
  first_name           String
  last_name            String
  code                 String
  number_of_vr_glasses Int
  number_of_licenses   Int
  is_subscription      Boolean?                   @default(false)
  duration             Int                        @default(0)
  created_at           DateTime                   @default(now())
  updated_at           DateTime                   @updatedAt
  order_number         String                     @db.VarChar(100)
  additional_info      purchase_additional_info[]
  orderStatus          orderStatus?               @relation("PurchaseToOrderStatus")
  shipping_Info        shipping_info?             @relation("PurchaseToShipping")

  @@map("purchase")
}

model activation {
  id              Int       @id @default(autoincrement())
  activation_date DateTime? @db.DateTime(0)
  purchase_id     Int
  updated_at      DateTime? @updatedAt @db.DateTime(0)
  user_id         Int?

  @@index([purchase_id], map: "purchase_activation_purchase_id_fk")
  @@index([user_id], map: "purchase_activation_user_id_fk")
  @@map("purchase_activation")
}

model userSurvey {
  id             Int  @id @default(autoincrement())
  user_id        Int?
  question_index Int?
  answer_index   Int?

  @@index([user_id], map: "userId")
  @@map("user_survey")
}

model school {
  id   Int    @id @default(autoincrement())
  name String @db.VarChar(255)
  city String @db.VarChar(255)

  @@map("school")
}

model binogiPermissions {
  id                Int  @id @default(autoincrement())
  school_id         Int
  binogi_section_id Int?

  @@index([school_id], map: "binogi_permissions_school_FK")
  @@map("binogi_permissions")
}

model schoolClass {
  id       Int    @id @default(autoincrement())
  name     String @db.VarChar(255)
  schoolId Int    @map("school_id")

  @@index([schoolId], map: "school_id")
  @@map("school_class")
}

model teacher {
  id               Int     @id @default(autoincrement())
  teacher_uuid     String? @db.VarChar(100)
  first_name       String? @db.VarChar(100)
  last_name        String? @db.VarChar(100)
  role             String? @db.VarChar(100)
  report_frequency Int?    @default(0)
}

model student {
  id          Int       @id @default(autoincrement())
  userId      Int?      @unique(map: "user_id") @map("user_id")
  password    String?   @db.VarChar(255)
  classId     Int?      @map("class_id")
  first_name  String?   @db.VarChar(255)
  last_name   String?   @db.VarChar(255)
  birthdate   DateTime? @db.Date
  email       String?   @db.VarChar(255)
  purchase_id Int?

  @@index([classId], map: "class_id")
  @@index([purchase_id], map: "purchase_id")
  @@map("student")
}

model studentTeacher {
  id        Int @id @default(autoincrement())
  studentId Int @map("student_id")
  teacherId Int @map("teacher_id")

  @@index([studentId], map: "student_id")
  @@index([teacherId], map: "teacher_id")
  @@map("student_teacher")
}

model training_session_data {
  id               Int      @id @default(autoincrement())
  user_id          Int?
  session_number   Int
  session_duration Decimal? @db.Decimal(10, 5)
  start_time       DateTime @db.DateTime(0)
  type             String   @db.VarChar(50)
  streamingSource  String?  @db.VarChar(100)
  speed            Decimal  @default(0.00000) @db.Decimal(10, 5)
  pendlumLength    Decimal  @default(0.00000) @db.Decimal(10, 5)
  offset           Decimal  @default(0.00000) @db.Decimal(10, 5)
  oscillationTime  Decimal  @default(0.00000) @db.Decimal(10, 5)
  deviceId         Int?     @map("device_id")

  @@index([user_id], map: "user_id")
  @@index([deviceId], map: "fk_training_session_data_device")
}

model shipping_info {
  id              Int      @id @default(autoincrement())
  purchase_id     Int      @unique
  shipping_date   DateTime @default(now())
  tracking_number String
  purchase        Purchase @relation("PurchaseToShipping", fields: [purchase_id], references: [id])

  @@index([purchase_id], map: "purchase_shipping_info_purchase_id_fkey")
  @@map("purchase_shipping_info")
}

model orderStatus {
  id          Int       @id @default(autoincrement())
  order_id    String    @unique
  purchase_id Int?      @unique
  status      String
  purchase    Purchase? @relation("PurchaseToOrderStatus", fields: [purchase_id], references: [id])

  @@index([purchase_id], map: "purchase_order_status_purchase_id_fkey")
  @@map("purchase_order_status")
}

model account {
  id                       String              @id @default(cuid())
  userId                   String              @unique @map("user_id")
  role                     admin_accounts_role @default(USER)
  provider                 String
  providerAccountId        String              @map("provider_account_id")
  refresh_token            String?             @db.Text
  access_token             String?             @db.Text
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?             @db.Text
  session_state            String?
  refresh_token_expires_in Int?
  createdAt                DateTime            @default(now())
  updatedAt                DateTime            @updatedAt
  user                     user_login          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("admin_accounts")
}

model user_login {
  id            String           @id @default(cuid())
  name          String?
  email         String?          @unique
  emailVerified DateTime?
  password      String?          @db.VarChar(250)
  role          String?          @default("admin") @db.VarChar(20)
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  accounts      account?
  activities    admin_activity[]
  authenticator authenticator[]
  sessions      login_session[]

  @@map("admin_user")
}

model login_session {
  id           String     @id @default(cuid())
  sessionToken String     @unique @map("session_token")
  userId       String     @map("user_id")
  expires      DateTime
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  user         user_login @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "login_sessions_user_id_fkey")
  @@map("login_sessions")
}

model verification_token {
  id         String   @id @default(cuid())
  identifier String
  token      String
  expires    DateTime
  createdAt  DateTime @default(now())

  @@unique([identifier, token])
}

model authenticator {
  credentialID         String     @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  user                 user_login @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

model purchase_additional_info {
  id              Int                                       @id @default(autoincrement())
  purchase_id     Int
  info            String                                    @db.Text
  is_hidden       Boolean?                                  @default(false)
  purchase_source purchase_additional_info_purchase_source?
  purchase_type   PurchaseType?
  country         String?                                   @db.VarChar(100)
  order_amount    Decimal?                                  @db.Decimal(10, 2)
  address_line    String?                                   @db.VarChar(255)
  city            String?                                   @db.VarChar(100)
  state           String?                                   @db.VarChar(100)
  postal_code     String?                                   @db.VarChar(20)
  phone           String?                                   @db.VarChar(50)
  purchase        Purchase                                  @relation(fields: [purchase_id], references: [id])

  @@index([purchase_id])
  @@map("purchase_additional_info")
}

model admin_activity {
  id         Int         @id @default(autoincrement())
  admin_id   String?
  action     String      @db.VarChar(255)
  entity     String?     @db.VarChar(255)
  entity_id  String?
  details    String?     @db.LongText
  ip_address String?     @db.VarChar(45)
  created_at DateTime    @default(now())
  admin      user_login? @relation(fields: [admin_id], references: [id])

  @@index([admin_id])
  @@index([created_at])
  @@map("admin_activities")
}

model training_config {
  id                   Int       @id @default(autoincrement())
  user_id              Int
  left_eye_offset_x    Int?
  left_eye_offset_y    Int?
  left_eye_speed_x     Int?
  left_eye_speed_y     Int?
  left_eye_pendulum_x  Int?
  left_eye_pendulum_y  Int?
  right_eye_offset_x   Int?
  right_eye_offset_y   Int?
  right_eye_speed_x    Int?
  right_eye_speed_y    Int?
  right_eye_pendulum_x Int?
  right_eye_pendulum_y Int?
  created_at           DateTime? @db.DateTime(0)
}

model ReadingSentencesChain {
  id               Int    @id @default(autoincrement())
  sentenceId       Int    @map("sentence_id")
  language         String @db.VarChar(50)
  sentence         String @db.Text
  correctPosition1 Int    @map("correct_position_1")
  correctPosition2 Int    @map("correct_position_2")

  @@map("reading_sentences_chain")
}

model ReadingSentencesChainCorrectTime {
  id      Int   @id @default(autoincrement())
  chainId Int   @map("chain_id")
  time    Float @db.Float

  @@index([chainId], map: "chain_id")
  @@map("reading_sentences_chain_correct_times")
}

model ReadingSentencesChainWrongTime {
  id      Int   @id @default(autoincrement())
  chainId Int   @map("chain_id")
  time    Float @db.Float

  @@index([chainId], map: "chain_id")
  @@map("reading_sentences_chain_wrong_times")
}

model ReadingLettersChain {
  id               Int    @id @default(autoincrement())
  letterId         Int    @map("letter_id")
  language         String @db.VarChar(50)
  letter           String @db.Text
  correctPosition1 Int    @map("correct_position_1")
  correctPosition2 Int    @map("correct_position_2")

  @@map("reading_letters_chain")
}

model ReadingLettersChainCorrectTime {
  id      Int   @id @default(autoincrement())
  chainId Int   @map("chain_id")
  time    Float @db.Float

  @@index([chainId], map: "chain_id")
  @@map("reading_letters_chain_correct_times")
}

model ReadingLettersChainWrongTime {
  id      Int   @id @default(autoincrement())
  chainId Int   @map("chain_id")
  time    Float @db.Float

  @@index([chainId], map: "chain_id")
  @@map("reading_letters_chain_wrong_times")
}

model ReadingWordsChain {
  id               Int    @id @default(autoincrement())
  wordId           Int    @map("word_id")
  language         String @db.VarChar(50)
  word             String @db.Text
  correctPosition1 Int    @map("correct_position_1")
  correctPosition2 Int    @map("correct_position_2")

  @@map("reading_words_chain")
}

model ReadingWordsChainCorrectTime {
  id      Int   @id @default(autoincrement())
  chainId Int   @map("chain_id")
  time    Float @db.Float

  @@index([chainId], map: "chain_id")
  @@map("reading_words_chain_correct_times")
}

model ReadingWordsChainWrongTime {
  id      Int   @id @default(autoincrement())
  chainId Int   @map("chain_id")
  time    Float @db.Float

  @@index([chainId], map: "chain_id")
  @@map("reading_words_chain_wrong_times")
}

model ReadingTestData {
  id       Int      @id @default(autoincrement())
  userId   Int      @map("user_id")
  testDate DateTime @map("test_date") @db.DateTime(0)
  language String   @db.VarChar(50)
  testType String   @map("test_type") @db.VarChar(50)
  score    Int

  @@index([userId], map: "user_id")
  @@map("reading_test_data")
}

model UserCorrectAnswerTime {
  id         Int    @id @default(autoincrement())
  testDataId Int    @map("test_data_id")
  chainId    Int    @map("chain_id")
  time       String @db.VarChar(10)
  value      Int

  @@index([testDataId], map: "test_data_id")
  @@map("user_correct_answer_times")
}

model UserWrongAnswerTime {
  id         Int    @id @default(autoincrement())
  testDataId Int    @map("test_data_id")
  chainId    Int    @map("chain_id")
  time       String @db.VarChar(10)
  value      Int

  @@index([testDataId], map: "test_data_id")
  @@map("user_wrong_answer_times")
}

model admin_notifications {
  id        Int                      @id @default(autoincrement())
  title     String                   @db.VarChar(255)
  message   String                   @db.Text
  read      Boolean?                 @default(false)
  type      admin_notifications_type
  metadata  String?                  @db.LongText
  createdAt DateTime?                @default(now()) @db.Timestamp(0)
  updatedAt DateTime?                @default(now()) @updatedAt @db.Timestamp(0)
}

model guardian {
  id           Int     @id @default(autoincrement())
  name         String? @db.VarChar(255)
  mobileNumber String? @db.VarChar(255)
  email        String? @db.VarChar(255)
}

enum PurchaseType {
  DEFAULT
  SUBSCRIPTION
  START_PACKAGE
  CONTINUE_TRAINING
}

enum admin_accounts_role {
  SUPERADMIN
  ADMIN
  SUBADMIN
  EDITOR
  TEACHER
  USER
}

enum admin_notifications_type {
  SHIPPING_MISSING
  PURCHASE_STATUS
  TRAINING_REMINDER
}

enum purchase_additional_info_purchase_source {
  DEFAULT
  WEBSHOP
  ADMIN
  IMPORTED
}
