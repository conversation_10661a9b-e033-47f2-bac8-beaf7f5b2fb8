{"name": "orders-tracker", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroui/react": "2.6.14", "@internationalized/date": "^3.7.0", "@tabler/icons-react": "^3.33.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "crypto-random-string": "^5.0.0", "firebase-admin": "^13.0.1", "framer-motion": "^11.15.0", "joi": "^17.13.3", "lucide-react": "^0.471.0", "moment": "^2.30.1", "next": "15.1.7", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-flatpickr": "^4.0.10", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "swr": "^2.2.5", "tslib": "^2.3.0", "zustand": "^5.0.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "autoprefixer": "10.4.13", "babel-jest": "^29.7.0", "esbuild": "^0.19.2", "eslint": "~8.57.0", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "postcss": "^8", "prettier": "^2.6.2", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "^5"}}